#!/usr/bin/env python3
"""
Orders Agent - Agente especializado en gestión de órdenes

Este agente puede responder preguntas sobre órdenes utilizando la base de datos
de órdenes y proporcionando respuestas en lenguaje natural.
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Cargar variables de entorno
env_file = Path(__file__).parent.parent.parent / ".env"
if env_file.exists():
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)
    except ImportError:
        logger.warning("python-dotenv no está disponible. Usando variables de entorno del sistema.")
        # Cargar manualmente las variables más importantes
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ.setdefault(key.strip(), value.strip())

# Importar dependencias de ADK
from google.adk.agents import Agent
from google.adk.tools.base_tool import BaseTool
from google.genai import types
import json

# Importar módulos del orders agent
from database import orders_db_manager
from order_tools import (
    search_orders_by_status_function,
    search_orders_by_date_function,
    search_orders_by_customer_function,
    get_order_statistics_function
)

logger = logging.getLogger(__name__)


class OrderSearchTool(BaseTool):
    """Herramienta para búsqueda de órdenes por estado"""
    
    def __init__(self):
        super().__init__(
            name="search_orders_by_status",
            description="Buscar órdenes por estado (pending, completed, cancelled, processing, shipped)",
            input_schema=types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "status": types.Schema(
                        type=types.Type.STRING,
                        description="Estado de las órdenes a buscar (pending, completed, cancelled, processing, shipped)"
                    )
                },
                required=["status"]
            )
        )
    
    async def run(self, status: str) -> str:
        return await search_orders_by_status_function(status)


class OrderDateSearchTool(BaseTool):
    """Herramienta para búsqueda de órdenes por fecha"""
    
    def __init__(self):
        super().__init__(
            name="search_orders_by_date",
            description="Buscar órdenes por fecha o rango de fechas (hoy, ayer, esta semana, etc.)",
            input_schema=types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "date_query": types.Schema(
                        type=types.Type.STRING,
                        description="Consulta de fecha: 'hoy', 'ayer', 'esta semana', 'último mes', o fecha específica en formato YYYY-MM-DD"
                    )
                },
                required=["date_query"]
            )
        )
    
    async def run(self, date_query: str) -> str:
        return await search_orders_by_date_function(date_query)


class OrderCustomerSearchTool(BaseTool):
    """Herramienta para búsqueda de órdenes por cliente"""
    
    def __init__(self):
        super().__init__(
            name="search_orders_by_customer",
            description="Buscar órdenes por nombre de cliente",
            input_schema=types.Schema(
                type=types.Type.OBJECT,
                properties={
                    "customer_name": types.Schema(
                        type=types.Type.STRING,
                        description="Nombre del cliente (búsqueda parcial permitida)"
                    )
                },
                required=["customer_name"]
            )
        )
    
    async def run(self, customer_name: str) -> str:
        return await search_orders_by_customer_function(customer_name)


class OrderStatisticsTool(BaseTool):
    """Herramienta para obtener estadísticas de órdenes"""
    
    def __init__(self):
        super().__init__(
            name="get_order_statistics",
            description="Obtener estadísticas generales de órdenes (totales, promedios, conteos por estado)",
            input_schema=types.Schema(
                type=types.Type.OBJECT,
                properties={}
            )
        )
    
    async def run(self) -> str:
        return await get_order_statistics_function()


async def ensure_orders_initialized():
    """
    Asegurar que el sistema de órdenes esté inicializado
    """
    try:
        # Inicializar base de datos
        await orders_db_manager.initialize()

        # Insertar datos de ejemplo si no existen órdenes
        stats = await orders_db_manager.get_order_statistics()
        if stats['total_orders'] == 0:
            logger.info("No hay órdenes en la base de datos. Insertando datos de ejemplo...")
            await orders_db_manager.insert_sample_orders()
            logger.info("Datos de ejemplo insertados correctamente")

        logger.info("Sistema de órdenes inicializado correctamente")
        return True

    except Exception as e:
        logger.warning(f"Error inicializando sistema de órdenes: {e}")
        logger.warning("El agente funcionará sin capacidades de base de datos")
        return False


def create_orders_agent():
    """
    Crear el agente de órdenes con todas las herramientas configuradas
    """
    # Configurar modelo
    model_name = os.getenv("DEFAULT_MODEL", "gemini-2.0-flash")
    
    try:
        model = LiteLlm(
            model=model_name,
            temperature=float(os.getenv("TEMPERATURE", "0.3")),
            max_tokens=int(os.getenv("MAX_OUTPUT_TOKENS", "2048"))
        )
    except Exception as e:
        logger.warning(f"Error configurando LiteLlm: {e}. Usando modelo por defecto.")
        model = model_name
    
    # Crear herramientas
    tools = [
        OrderSearchTool(),
        OrderDateSearchTool(),
        OrderCustomerSearchTool(),
        OrderStatisticsTool()
    ]
    
    # Crear agente de órdenes
    orders_agent = Agent(
        model=model,
        name="orders_management_agent",
        description="Agente especializado en gestión y consulta de órdenes. Puede buscar órdenes por estado, fecha, cliente y proporcionar estadísticas.",
        instruction="""
        Eres un asistente especializado en GESTIÓN DE ÓRDENES. Responde ÚNICAMENTE preguntas sobre órdenes usando las herramientas disponibles.

        PROCESO:
        1. Si la pregunta es sobre órdenes, usa las herramientas apropiadas para buscar información
        2. Si la pregunta NO es sobre órdenes, responde: "Solo puedo responder preguntas sobre órdenes. Por favor, pregúntame algo relacionado con órdenes."
        3. Siempre proporciona respuestas claras y organizadas en español
        4. Incluye detalles relevantes como números de orden, clientes, fechas y montos
        5. Si no encuentras información, explica claramente que no hay datos disponibles

        CAPACIDADES:
        - Buscar órdenes por estado (pendientes, completadas, canceladas, etc.)
        - Buscar órdenes por fecha (hoy, ayer, esta semana, fechas específicas)
        - Buscar órdenes por cliente
        - Proporcionar estadísticas generales de órdenes
        - Mostrar detalles completos de órdenes incluyendo productos

        FORMATO DE RESPUESTA:
        - Usa formato claro y organizado
        - Incluye números y estadísticas cuando sea relevante
        - Menciona fechas en formato legible
        - Organiza la información por orden de relevancia
        
        Responde siempre en español y mantén un tono profesional y útil.
        """,
        tools=tools
    )
    
    return orders_agent


# Crear el agente raíz
try:
    root_agent = create_orders_agent()
    logger.info("Agente de órdenes creado correctamente")
except Exception as e:
    logger.error(f"Error creando agente de órdenes: {e}")
    # Crear un agente básico de fallback
    from google.adk.agents import Agent
    root_agent = Agent(
        model="gemini-2.0-flash",
        name="orders_basic_agent",
        description="Agente básico de órdenes",
        instruction="Soy un agente especializado en órdenes. Actualmente estoy en modo básico."
    )
    logger.info("Agente de órdenes básico creado como fallback")
